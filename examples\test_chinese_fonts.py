#!/usr/bin/env python3
"""
测试中文字体显示

验证matplotlib中文字符显示是否正常工作
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import numpy as np
import pandas as pd
from pathlib import Path
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

# 导入我们的可视化模块
from time_series_insight.visualization.plotter import TimeSeriesPlotter


def test_font_availability():
    """测试系统可用字体"""
    print("=" * 60)
    print("系统字体检测")
    print("=" * 60)
    
    # 获取所有可用字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    
    # 检查常见中文字体
    chinese_fonts = [
        'Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi', 'FangSong',  # Windows
        'PingFang SC', 'Hiragino Sans GB', 'STHeiti',  # macOS
        'WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', 'Noto Sans CJK SC'  # Linux
    ]
    
    print("检测到的中文字体:")
    found_fonts = []
    for font in chinese_fonts:
        if font in available_fonts:
            found_fonts.append(font)
            print(f"  ✓ {font}")
    
    if not found_fonts:
        print("  ⚠️  未检测到常见中文字体")
        print("  建议安装中文字体包以获得更好的显示效果")
    
    print(f"\n当前matplotlib字体设置:")
    print(f"  font.sans-serif: {plt.rcParams['font.sans-serif']}")
    print(f"  axes.unicode_minus: {plt.rcParams['axes.unicode_minus']}")
    
    return found_fonts


def test_chinese_text_rendering():
    """测试中文文本渲染"""
    print("\n" + "=" * 60)
    print("中文文本渲染测试")
    print("=" * 60)
    
    # 创建测试图表
    fig, ax = plt.subplots(figsize=(10, 6))
    
    # 测试各种中文文本
    test_texts = [
        "时间序列分析",
        "自相关函数 (ACF)",
        "偏自相关函数 (PACF)",
        "预测结果",
        "残差分析",
        "模型比较",
        "数据可视化"
    ]
    
    # 绘制测试文本
    y_positions = np.linspace(0.1, 0.9, len(test_texts))
    
    for i, text in enumerate(test_texts):
        ax.text(0.1, y_positions[i], text, fontsize=14, 
               transform=ax.transAxes, verticalalignment='center')
        ax.text(0.6, y_positions[i], f"测试文本 {i+1}", fontsize=12,
               transform=ax.transAxes, verticalalignment='center')
    
    ax.set_xlim(0, 1)
    ax.set_ylim(0, 1)
    ax.set_title("中文字体显示测试", fontsize=16, fontweight='bold')
    ax.set_xlabel("横轴标签 (X轴)", fontsize=12)
    ax.set_ylabel("纵轴标签 (Y轴)", fontsize=12)
    
    # 移除坐标轴刻度
    ax.set_xticks([])
    ax.set_yticks([])
    
    # 保存测试图片
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    
    test_file = output_dir / "chinese_font_test.png"
    plt.savefig(test_file, dpi=300, bbox_inches='tight')
    print(f"中文字体测试图片已保存: {test_file}")
    
    plt.close()
    return test_file


def test_time_series_visualization():
    """测试时间序列可视化中的中文显示"""
    print("\n" + "=" * 60)
    print("时间序列可视化中文显示测试")
    print("=" * 60)
    
    # 创建测试数据
    np.random.seed(42)
    dates = pd.date_range('2023-01-01', periods=100, freq='D')
    values = np.cumsum(np.random.randn(100)) + 100
    data = pd.Series(values, index=dates)
    
    # 创建可视化器
    plotter = TimeSeriesPlotter()
    
    # 创建输出目录
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    
    # 测试时间序列图
    print("1. 测试时间序列图...")
    fig1 = plotter.plot_time_series(
        data, 
        title="时间序列数据可视化测试",
        show_trend=True,
        save_path=output_dir / "time_series_chinese_test.png"
    )
    plt.close(fig1)
    print(f"   已保存: {output_dir / 'time_series_chinese_test.png'}")
    
    # 测试ACF/PACF图
    print("2. 测试ACF/PACF图...")
    fig2 = plotter.plot_acf_pacf(
        data.diff().dropna(),
        title="自相关和偏自相关函数分析",
        save_path=output_dir / "acf_pacf_chinese_test.png"
    )
    plt.close(fig2)
    print(f"   已保存: {output_dir / 'acf_pacf_chinese_test.png'}")
    
    print("\n测试图片已保存到 examples/output/ 目录")
    print("请检查图片中的中文字符是否正常显示")


def main():
    """运行所有测试"""
    print("中文字体显示测试程序")
    print("=" * 60)
    
    try:
        # 1. 检测字体
        found_fonts = test_font_availability()
        
        # 2. 测试文本渲染
        test_chinese_text_rendering()
        
        # 3. 测试时间序列可视化
        test_time_series_visualization()
        
        print("\n" + "=" * 60)
        print("测试完成！")
        print("=" * 60)
        
        if found_fonts:
            print("✓ 检测到中文字体，应该能正常显示中文")
        else:
            print("⚠️  未检测到中文字体，可能需要安装中文字体包")
            print("\n建议解决方案:")
            print("1. Windows: 确保系统已安装微软雅黑等中文字体")
            print("2. macOS: 系统通常自带中文字体")
            print("3. Linux: 安装中文字体包，如 fonts-wqy-microhei")
        
        print(f"\n请查看 examples/output/ 目录中的测试图片")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

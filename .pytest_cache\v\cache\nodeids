["tests/test_api.py::TestDataTypes::test_different_data_types", "tests/test_api.py::TestTimeSeriesInsight::test_analyze", "tests/test_api.py::TestTimeSeriesInsight::test_analyze_time_series_function", "tests/test_api.py::TestTimeSeriesInsight::test_error_handling_no_analysis", "tests/test_api.py::TestTimeSeriesInsight::test_error_handling_no_data", "tests/test_api.py::TestTimeSeriesInsight::test_export_results_json", "tests/test_api.py::TestTimeSeriesInsight::test_get_best_model", "tests/test_api.py::TestTimeSeriesInsight::test_get_summary", "tests/test_api.py::TestTimeSeriesInsight::test_load_data", "tests/test_api.py::TestTimeSeriesInsight::test_plot_analysis", "tests/test_api.py::TestTimeSeriesInsight::test_predict", "tests/test_api.py::TestTimeSeriesInsight::test_quick_analysis", "tests/test_data_processor.py::TestTimeSeriesProcessor::test_auto_difference", "tests/test_data_processor.py::TestTimeSeriesProcessor::test_check_stationarity_non_stationary", "tests/test_data_processor.py::TestTimeSeriesProcessor::test_check_stationarity_stationary", "tests/test_data_processor.py::TestTimeSeriesProcessor::test_clean_data_with_missing_values", "tests/test_data_processor.py::TestTimeSeriesProcessor::test_difference", "tests/test_data_processor.py::TestTimeSeriesProcessor::test_empty_data", "tests/test_data_processor.py::TestTimeSeriesProcessor::test_get_processed_data", "tests/test_data_processor.py::TestTimeSeriesProcessor::test_get_summary", "tests/test_data_processor.py::TestTimeSeriesProcessor::test_invalid_data_type", "tests/test_data_processor.py::TestTimeSeriesProcessor::test_load_array_data", "tests/test_data_processor.py::TestTimeSeriesProcessor::test_load_csv_file", "tests/test_data_processor.py::TestTimeSeriesProcessor::test_load_dataframe_data", "tests/test_data_processor.py::TestTimeSeriesProcessor::test_load_series_data"]